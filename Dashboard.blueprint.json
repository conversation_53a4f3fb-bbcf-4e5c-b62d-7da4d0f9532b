{"name": "Dashboard", "flow": [{"id": 1, "module": "gateway:CustomWebHook", "version": 1, "parameters": {"hook": 2389716, "maxResults": 1}, "mapper": {}, "metadata": {"designer": {"x": 0, "y": 0}, "restore": {"parameters": {"hook": {"data": {"editable": "true"}, "label": "Thyme And Whisk (Dashboard)"}}}, "parameters": [{"name": "hook", "type": "hook:gateway-webhook", "label": "Webhook", "required": true}, {"name": "maxResults", "type": "number", "label": "Maximum number of results"}]}}, {"id": 4, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": true, "useNewZLibDeCompress": true}, "mapper": {"url": "https://voice-assistant-dashboard-seven.vercel.app/api/webhook", "serializeUrl": false, "method": "post", "headers": [{"name": "Content-Type", "value": "application/json"}], "qs": [], "bodyType": "raw", "parseResponse": false, "authUser": "", "authPass": "", "timeout": "", "shareCookies": false, "ca": "", "rejectUnauthorized": true, "followRedirect": true, "useQuerystring": false, "gzip": true, "useMtls": false, "contentType": "application/json", "data": "{\r\n  \"id\": \"{{1.id}}\",\r\n  \"caller_name\": \"{{1.message.analysis.structuredData._name}}\",\r\n  \"call_start\": \"{{1.message.startedAt}}\",\r\n  \"call_end\": \"{{1.message.endedAt}}\",\r\n  \"transcript\": \"{{1.message.summary}}\",\r\n  \"success_flag\": {{1.message.analysis.successEvaluation}},\r\n  \"cost\": {{1.message.cost}}\r\n}", "followAllRedirects": false}, "metadata": {"designer": {"x": 300, "y": 0}, "restore": {"expect": {"method": {"mode": "chose", "label": "POST"}, "headers": {"mode": "chose", "items": [null]}, "qs": {"mode": "chose"}, "bodyType": {"label": "Raw"}, "contentType": {"label": "JSON (application/json)"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "type": "array", "label": "Headers", "spec": [{"name": "name", "label": "Name", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text"}]}, {"name": "qs", "type": "array", "label": "Query String", "spec": [{"name": "name", "label": "Name", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text"}]}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "contentType", "type": "select", "label": "Content type", "validate": {"enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]}}, {"name": "data", "type": "buffer", "label": "Request content"}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}}], "metadata": {"instant": true, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": [[{"id": 3, "module": "google-sheets:addRow", "version": 2, "parameters": {"__IMTCONN__": 2361663}, "mapper": {"from": "drive", "mode": "select", "values": {"0": "{{1.message.analysis.structuredData.`_name`}}", "2": "{{1.message.startedAt}}", "3": "{{1.message.endedAt}}", "4": "{{1.message.summary}}", "5": "{{1.message.cost}}", "6": "{{1.message.analysis.successEvaluation}}"}, "sheetId": "Sheet1", "spreadsheetId": "/1cTAP9MPx5QgVPIEHFMk2Z5VVsfkkbivxr4_Kt1z5h2Q", "includesHeaders": true, "insertDataOption": "INSERT_ROWS", "valueInputOption": "USER_ENTERED", "insertUnformatted": false}, "metadata": {"designer": {"x": 0, "y": 306, "messages": [{"category": "link", "severity": "warning", "message": "The module is not connected to the data flow."}, {"category": "reference", "severity": "warning", "message": "Referenced module 'Webhooks - Custom webhook' [1] is not accessible."}]}, "restore": {"expect": {"from": {"label": "My Drive"}, "mode": {"label": "Search by path"}, "sheetId": {"label": "Sheet1"}, "spreadsheetId": {"path": ["Dashboard (Thyme and Whisk)"]}, "includesHeaders": {"label": "Yes", "nested": [{"name": "values", "spec": [{"name": "0", "type": "text", "label": "caller_name (A)"}, {"name": "1", "type": "text", "label": "phone (B)"}, {"name": "2", "type": "text", "label": "call_start (C)"}, {"name": "3", "type": "text", "label": "call_end (D)"}, {"name": "4", "type": "text", "label": "transcript (E)"}, {"name": "5", "type": "text", "label": "cost (F)"}, {"name": "6", "type": "text", "label": "success_flag (G)"}, {"name": "7", "type": "text", "label": "(H)"}, {"name": "8", "type": "text", "label": "(I)"}, {"name": "9", "type": "text", "label": "(J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}, "insertDataOption": {"mode": "chose", "label": "Insert rows"}, "valueInputOption": {"mode": "chose", "label": "User entered"}, "insertUnformatted": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "Scripts  (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "mode", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "insertUnformatted", "type": "boolean", "label": "Unformatted", "required": true}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "insertDataOption", "type": "select", "label": "Insert data option", "validate": {"enum": ["INSERT_ROWS", "OVERWRITE"]}}, {"name": "from", "type": "select", "label": "Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "spreadsheetId", "type": "file", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "caller_name (A)"}, {"name": "1", "type": "text", "label": "phone (B)"}, {"name": "2", "type": "text", "label": "call_start (C)"}, {"name": "3", "type": "text", "label": "call_end (D)"}, {"name": "4", "type": "text", "label": "transcript (E)"}, {"name": "5", "type": "text", "label": "cost (F)"}, {"name": "6", "type": "text", "label": "success_flag (G)"}, {"name": "7", "type": "text", "label": "(H)"}, {"name": "8", "type": "text", "label": "(I)"}, {"name": "9", "type": "text", "label": "(J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}}]]}, "zone": "eu2.make.com", "notes": []}}