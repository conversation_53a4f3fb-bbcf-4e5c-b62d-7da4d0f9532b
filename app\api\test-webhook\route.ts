import { NextResponse } from 'next/server'

// Test endpoint to simulate webhook data
export async function GET() {
  // Simulate webhook response data
  const mockWebhookData = [
    {
      id: "call_001",
      caller_name: "<PERSON><PERSON>",
      phone: "+91 98765 43210",
      call_start: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
      call_end: new Date(Date.now() - 1000 * 60 * 13).toISOString(),
      duration: "2m 15s",
      transcript: "I would like to book a table for 4 people tomorrow at 7 PM for dinner.",
      success_flag: true,
      cost: 125.50
    },
    {
      id: "call_002", 
      caller_name: "<PERSON><PERSON>",
      phone: "+91 87654 32109",
      call_start: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
      call_end: new Date(Date.now() - 1000 * 60 * 43).toISOString(),
      duration: "1m 45s",
      transcript: "What are your operating hours and do you take reservations?",
      success_flag: false,
      cost: 89.25
    },
    {
      id: "call_003",
      caller_name: "<PERSON><PERSON>",
      phone: "+91 76543 21098", 
      call_start: new Date(Date.now() - 1000 * 60 * 120).toISOString(),
      call_end: new Date(Date.now() - 1000 * 60 * 117).toISOString(),
      duration: "3m 12s",
      transcript: "I need to make a reservation for a birthday party of 8 people this Saturday evening.",
      success_flag: true,
      cost: 156.75
    },
    {
      id: "call_004",
      caller_name: "Neha Singh",
      phone: "+91 65432 10987",
      call_start: new Date(Date.now() - 1000 * 60 * 180).toISOString(), 
      call_end: new Date(Date.now() - 1000 * 60 * 179).toISOString(),
      duration: "0m 58s",
      transcript: "Do you have vegan options on your menu?",
      success_flag: false,
      cost: 45.00
    },
    {
      id: "call_005",
      caller_name: "Vikram Mehta",
      phone: "+91 54321 09876",
      call_start: new Date(Date.now() - 1000 * 60 * 240).toISOString(),
      call_end: new Date(Date.now() - 1000 * 60 * 237).toISOString(), 
      duration: "2m 45s",
      transcript: "I want to book a table for my anniversary dinner next Friday at 8 PM.",
      success_flag: true,
      cost: 134.25
    },
    {
      id: "call_006",
      caller_name: "Anjali Gupta", 
      phone: "+91 43210 98765",
      call_start: new Date(Date.now() - 1000 * 60 * 300).toISOString(),
      call_end: new Date(Date.now() - 1000 * 60 * 298).toISOString(),
      duration: "1m 30s",
      transcript: "Can I get directions to your restaurant location?",
      success_flag: false,
      cost: 67.50
    },
    {
      id: "call_007",
      caller_name: "Sanjay Verma",
      phone: "+91 32109 87654",
      call_start: new Date(Date.now() - 1000 * 60 * 360).toISOString(),
      call_end: new Date(Date.now() - 1000 * 60 * 356).toISOString(),
      duration: "4m 15s", 
      transcript: "I need to book a large table for a business meeting lunch for 12 people.",
      success_flag: true,
      cost: 198.75
    },
    {
      id: "call_008",
      caller_name: "Meera Joshi",
      phone: "+91 21098 76543",
      call_start: new Date(Date.now() - 1000 * 60 * 420).toISOString(),
      call_end: new Date(Date.now() - 1000 * 60 * 419).toISOString(),
      duration: "1m 12s",
      transcript: "What's your cancellation policy for reservations?",
      success_flag: false,
      cost: 58.00
    }
  ]

  return NextResponse.json(mockWebhookData)
}
