import { NextRequest, NextResponse } from 'next/server'

// In-memory storage for call data (in production, use a database)
let callDataStore: any[] = []

export async function POST(request: NextRequest) {
  try {
    console.log('Webhook POST request received')
    console.log('Headers:', Object.fromEntries(request.headers.entries()))

    const contentType = request.headers.get('content-type')
    console.log('Content-Type:', contentType)

    let body
    let rawText = ''
    try {
      rawText = await request.text()
      console.log('Raw request body:', rawText)

      // Try to fix common JSON issues with unescaped quotes in strings
      let fixedText = rawText

      // Fix unescaped quotes in summary field specifically
      const summaryMatch = rawText.match(/"summary":\s*"([^"]*(?:"[^"]*"[^"]*)*)"/)
      if (summaryMatch) {
        const originalSummary = summaryMatch[1]
        const fixedSummary = originalSummary.replace(/"/g, '\\"')
        fixedText = rawText.replace(
          `"summary": "${originalSummary}"`,
          `"summary": "${fixedSummary}"`
        )
        console.log('Fixed JSON with escaped quotes:', fixedText)
      }

      body = JSON.parse(fixedText)
      console.log('Parsed webhook data:', JSON.stringify(body, null, 2))
    } catch (parseError) {
      console.error('Failed to parse JSON:', parseError)
      console.log('Raw body that failed to parse:', rawText)

      // Try a more aggressive fix for quotes
      try {
        console.log('Attempting aggressive quote fixing...')
        let aggressivelyFixed = rawText

        // Replace unescaped quotes within string values (but not the structural quotes)
        aggressivelyFixed = aggressivelyFixed.replace(
          /"summary":\s*"([^"]+(?:"[^"]*"[^"]*)*[^"]*)"/g,
          (match, content) => {
            const escaped = content.replace(/"/g, '\\"')
            return `"summary": "${escaped}"`
          }
        )

        body = JSON.parse(aggressivelyFixed)
        console.log('Successfully parsed with aggressive fixing')
      } catch (secondError) {
        console.error('Aggressive fixing also failed:', secondError)
        return NextResponse.json(
          { success: false, error: `Invalid JSON format: ${parseError.message}` },
          { status: 400 }
        )
      }
    }

    // Validate that we have some data
    if (!body || (typeof body === 'object' && Object.keys(body).length === 0)) {
      console.error('Empty or invalid body received')
      return NextResponse.json(
        { success: false, error: 'Empty request body' },
        { status: 400 }
      )
    }

    // Add timestamp to the received data
    const callData = {
      ...body,
      received_at: new Date().toISOString(),
      id: body.id || `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    // Store the call data
    callDataStore.push(callData)

    // Keep only the last 100 calls to prevent memory issues
    if (callDataStore.length > 100) {
      callDataStore = callDataStore.slice(-100)
    }

    console.log(`Stored call data. Total calls: ${callDataStore.length}`)

    return NextResponse.json({
      success: true,
      message: 'Call data received and stored',
      totalCalls: callDataStore.length
    })

  } catch (error) {
    console.error('Error processing webhook:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to process webhook data' },
      { status: 400 }
    )
  }
}

export async function GET() {
  // Return stored call data for the dashboard
  return NextResponse.json(callDataStore)
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
