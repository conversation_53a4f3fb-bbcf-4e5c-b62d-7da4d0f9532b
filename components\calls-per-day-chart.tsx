"use client"

import { <PERSON>, <PERSON><PERSON>hart, Car<PERSON>ianG<PERSON>, Responsive<PERSON><PERSON>r, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

// Mock data - will be replaced with Google Sheets data
const data = [
  { date: "May 1", calls: 42 },
  { date: "May 2", calls: 38 },
  { date: "May 3", calls: 45 },
  { date: "May 4", calls: 39 },
  { date: "May 5", calls: 52 },
  { date: "May 6", calls: 47 },
  { date: "May 7", calls: 35 },
  { date: "May 8", calls: 62 },
  { date: "May 9", calls: 55 },
  { date: "May 10", calls: 58 },
  { date: "May 11", calls: 43 },
  { date: "May 12", calls: 40 },
  { date: "May 13", calls: 37 },
  { date: "May 14", calls: 45 },
]

export function CallsPerDayChart() {
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
          <defs>
            <linearGradient id="colorCalls" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#8B5CF6" stopOpacity={0.8} />
              <stop offset="95%" stopColor="#EC4899" stopOpacity={0.2} />
            </linearGradient>
          </defs>
          <XAxis
            dataKey="date"
            tick={{ fill: "#6B7280", fontSize: 12 }}
            axisLine={{ stroke: "#E5E7EB" }}
            tickLine={{ stroke: "#E5E7EB" }}
          />
          <YAxis
            tick={{ fill: "#6B7280", fontSize: 12 }}
            axisLine={{ stroke: "#E5E7EB" }}
            tickLine={{ stroke: "#E5E7EB" }}
          />
          <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
          <Tooltip
            contentStyle={{
              backgroundColor: "white",
              borderRadius: "0.5rem",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
              border: "1px solid #E5E7EB",
            }}
            labelStyle={{ fontWeight: "bold", color: "#4B5563" }}
            itemStyle={{ color: "#8B5CF6" }}
          />
          <Area
            type="monotone"
            dataKey="calls"
            stroke="#8B5CF6"
            strokeWidth={3}
            fillOpacity={1}
            fill="url(#colorCalls)"
            activeDot={{ r: 6, fill: "#EC4899", stroke: "white", strokeWidth: 2 }}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  )
}
