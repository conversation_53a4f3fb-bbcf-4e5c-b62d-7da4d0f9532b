"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { formatDistanceToNow } from "date-fns"
import { type CallData } from "@/lib/webhook-service"

interface RecentCallsTableProps {
  callData: CallData[]
}

export function RecentCallsTable({ callData }: RecentCallsTableProps) {
  // Only use real webhook data, no fallback
  const recentCalls = callData.slice(0, 10).map(call => ({
    id: call.id,
    caller_name: call.caller_name,
    phone: call.phone,
    duration: call.duration,
    timestamp: new Date(call.call_start),
    transcript: call.transcript,
    success: call.success_flag,
  }))

  if (recentCalls.length === 0) {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow className="bg-gradient-to-r from-purple-50 via-pink-50 to-blue-50">
              <TableHead>Caller</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Time</TableHead>
              <TableHead>Summary</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                No call data available. Waiting for webhook data...
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow className="bg-gradient-to-r from-purple-50 via-pink-50 to-blue-50">
            <TableHead>Caller</TableHead>
            <TableHead>Duration</TableHead>
            <TableHead>Time</TableHead>
            <TableHead>Summary</TableHead>
            <TableHead>Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {recentCalls.map((call) => (
            <TableRow
              key={call.id}
              className="hover:bg-gradient-to-r hover:from-purple-25 hover:via-pink-25 hover:to-blue-25"
            >
              <TableCell className="font-medium">{call.caller_name}</TableCell>
              <TableCell>{call.duration}</TableCell>
              <TableCell>{formatDistanceToNow(call.timestamp, { addSuffix: true })}</TableCell>
              <TableCell className="max-w-[200px]">
                <Dialog>
                  <DialogTrigger asChild>
                    <div className="truncate cursor-pointer text-blue-600 hover:text-blue-800 hover:underline">
                      {call.transcript}
                    </div>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>Call Summary</DialogTitle>
                    </DialogHeader>
                    <div className="mt-4">
                      <div className="mb-4">
                        <strong>Caller:</strong> {call.caller_name}
                      </div>
                      <div className="mb-4">
                        <strong>Duration:</strong> {call.duration}
                      </div>
                      <div className="mb-4">
                        <strong>Time:</strong> {formatDistanceToNow(call.timestamp, { addSuffix: true })}
                      </div>
                      <div>
                        <strong>Full Summary:</strong>
                        <div className="mt-2 p-4 bg-gray-50 rounded-lg whitespace-pre-wrap">
                          {call.transcript}
                        </div>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </TableCell>
              <TableCell>
                <span
                  className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                    call.success
                      ? "bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200"
                      : "bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-800 border border-orange-200"
                  }`}
                >
                  {call.success ? "Success" : "Incomplete"}
                </span>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
