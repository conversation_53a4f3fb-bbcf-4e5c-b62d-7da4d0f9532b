"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { formatDistanceToNow } from "date-fns"
import { type CallData } from "@/lib/webhook-service"

interface RecentCallsTableProps {
  callData: CallData[]
}

// Mock data fallback
const mockCalls = [
  {
    id: "1",
    caller_name: "<PERSON><PERSON>",
    phone: "+91 98765 43210",
    duration: "1m 45s",
    timestamp: new Date(Date.now() - 1000 * 60 * 15),
    transcript: "I'd like to book a table for two people tomorrow at 7 PM.",
    success: true,
  },
  {
    id: "2",
    caller_name: "<PERSON><PERSON>",
    phone: "+91 87654 32109",
    duration: "2m 12s",
    timestamp: new Date(Date.now() - 1000 * 60 * 45),
    transcript: "I need to make a reservation for a party of 6 on Friday evening.",
    success: true,
  },
  {
    id: "3",
    caller_name: "<PERSON><PERSON>",
    phone: "+91 76543 21098",
    duration: "0m 58s",
    timestamp: new Date(Date.now() - 1000 * 60 * 120),
    transcript: "What are your hours of operation?",
    success: false,
  },
  {
    id: "4",
    caller_name: "Neha Singh",
    phone: "+91 65432 10987",
    duration: "3m 24s",
    timestamp: new Date(Date.now() - 1000 * 60 * 180),
    transcript: "I'd like to book a table for four people on Saturday at 6:30 PM.",
    success: true,
  },
  {
    id: "5",
    caller_name: "Vikram Mehta",
    phone: "+91 54321 09876",
    duration: "1m 17s",
    timestamp: new Date(Date.now() - 1000 * 60 * 240),
    transcript: "Do you have any vegetarian options on the menu?",
    success: false,
  },
  {
    id: "6",
    caller_name: "Anjali Gupta",
    phone: "+91 43210 98765",
    duration: "2m 45s",
    timestamp: new Date(Date.now() - 1000 * 60 * 300),
    transcript: "I need to make a reservation for tomorrow at noon for a business lunch.",
    success: true,
  },
  {
    id: "7",
    caller_name: "Rajesh Verma",
    phone: "+91 32109 87654",
    duration: "0m 42s",
    timestamp: new Date(Date.now() - 1000 * 60 * 360),
    transcript: "What's your address?",
    success: false,
  },
  {
    id: "8",
    caller_name: "Meera Joshi",
    phone: "+91 21098 76543",
    duration: "4m 03s",
    timestamp: new Date(Date.now() - 1000 * 60 * 420),
    transcript: "I'd like to book a table for my anniversary dinner next week.",
    success: true,
  },
  {
    id: "9",
    caller_name: "Sanjay Malhotra",
    phone: "+91 10987 65432",
    duration: "2m 38s",
    timestamp: new Date(Date.now() - 1000 * 60 * 480),
    transcript: "I need to make a reservation for a group of 10 people for a birthday celebration.",
    success: true,
  },
  {
    id: "10",
    caller_name: "Pooja Reddy",
    phone: "+91 09876 54321",
    duration: "1m 52s",
    timestamp: new Date(Date.now() - 1000 * 60 * 540),
    transcript: "Do I need to make a deposit for a large party reservation?",
    success: false,
  },
]

export function RecentCallsTable({ callData }: RecentCallsTableProps) {
  // Use real data if available, otherwise fall back to mock data
  const recentCalls = callData.length > 0
    ? callData.slice(0, 10).map(call => ({
        id: call.id,
        caller_name: call.caller_name,
        phone: call.phone,
        duration: call.duration,
        timestamp: new Date(call.call_start),
        transcript: call.transcript,
        success: call.success_flag,
      }))
    : mockCalls

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow className="bg-gradient-to-r from-purple-50 via-pink-50 to-blue-50">
            <TableHead>Caller</TableHead>
            <TableHead>Phone</TableHead>
            <TableHead>Duration</TableHead>
            <TableHead>Time</TableHead>
            <TableHead>Transcript</TableHead>
            <TableHead>Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {recentCalls.map((call) => (
            <TableRow
              key={call.id}
              className="hover:bg-gradient-to-r hover:from-purple-25 hover:via-pink-25 hover:to-blue-25"
            >
              <TableCell className="font-medium">{call.caller_name}</TableCell>
              <TableCell>{call.phone}</TableCell>
              <TableCell>{call.duration}</TableCell>
              <TableCell>{formatDistanceToNow(call.timestamp, { addSuffix: true })}</TableCell>
              <TableCell className="max-w-[200px]">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="truncate">{call.transcript}</div>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="max-w-[300px]">
                      <p>{call.transcript}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </TableCell>
              <TableCell>
                <span
                  className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                    call.success
                      ? "bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200"
                      : "bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-800 border border-orange-200"
                  }`}
                >
                  {call.success ? "Success" : "Incomplete"}
                </span>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
