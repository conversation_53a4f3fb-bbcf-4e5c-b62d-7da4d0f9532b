// Webhook service for fetching call data from Make.com

export interface CallData {
  id: string
  caller_name: string
  phone: string
  call_start: Date
  call_end: Date
  duration: string
  transcript: string
  success_flag: boolean
  cost: number // Cost in rupees
}

export interface DashboardMetrics {
  totalCalls: number
  avgCallDuration: string
  totalBalance: string
  avgCallCost: string
  successRate: string
  totalReservations: number
  lastRefreshed: string
}

const WEBHOOK_URL = "https://hook.eu2.make.com/xr96mpnk747rzostpd736mxcm87axq7l"
const TEST_WEBHOOK_URL = "/api/test-webhook"
const INITIAL_BALANCE = 5000 // ₹5000 starting balance

// Cache for storing fetched data
let cachedData: CallData[] = []
let lastFetchTime = 0
let lastDataSource = ''
const CACHE_DURATION = 30000 // 30 seconds cache

export function getDataSource(): string {
  return lastDataSource
}

export async function fetchWebhookData(): Promise<CallData[]> {
  const now = Date.now()

  // Return cached data if it's still fresh
  if (cachedData.length > 0 && (now - lastFetchTime) < CACHE_DURATION) {
    return cachedData
  }

  // Try main webhook first, then fallback to test endpoint
  const urlsToTry = [WEBHOOK_URL, TEST_WEBHOOK_URL]

  for (const url of urlsToTry) {
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // Transform the webhook data to match our CallData interface
      const transformedData: CallData[] = Array.isArray(data) ? data.map((item: any, index: number) => ({
        id: item.id || `call_${index + 1}`,
        caller_name: item.caller_name || item.name || `Caller ${index + 1}`,
        phone: item.phone || item.phone_number || '+91 00000 00000',
        call_start: new Date(item.call_start || item.start_time || Date.now()),
        call_end: new Date(item.call_end || item.end_time || Date.now()),
        duration: item.duration || calculateDuration(item.call_start, item.call_end),
        transcript: item.transcript || item.message || 'No transcript available',
        success_flag: item.success_flag !== undefined ? item.success_flag : item.success || Math.random() > 0.3,
        cost: parseFloat(item.cost || item.call_cost || (Math.random() * 150 + 50).toFixed(2))
      })) : []

      cachedData = transformedData
      lastFetchTime = now
      lastDataSource = url === WEBHOOK_URL ? 'Make.com Webhook' : 'Test Data'

      return transformedData
    } catch (error) {
      console.error(`Error fetching from ${url}:`, error)
      // Continue to next URL
    }
  }

  // If all URLs failed, return cached data if available
  return cachedData.length > 0 ? cachedData : []
}

function calculateDuration(startTime: string | Date, endTime: string | Date): string {
  if (!startTime || !endTime) return '0m 0s'

  const start = new Date(startTime)
  const end = new Date(endTime)
  const diffMs = end.getTime() - start.getTime()
  const diffSeconds = Math.floor(diffMs / 1000)

  const minutes = Math.floor(diffSeconds / 60)
  const seconds = diffSeconds % 60

  return `${minutes}m ${seconds}s`
}

export async function calculateMetrics(data: CallData[]): Promise<DashboardMetrics> {
  const totalCalls = data.length
  const successfulCalls = data.filter(call => call.success_flag).length
  const totalCost = data.reduce((sum, call) => sum + call.cost, 0)
  const currentBalance = INITIAL_BALANCE - totalCost

  // Calculate average call duration
  let totalDurationSeconds = 0
  data.forEach(call => {
    const [minutes, seconds] = call.duration.split('m ')
    const mins = parseInt(minutes) || 0
    const secs = parseInt(seconds.replace('s', '')) || 0
    totalDurationSeconds += (mins * 60) + secs
  })

  const avgDurationSeconds = totalCalls > 0 ? Math.floor(totalDurationSeconds / totalCalls) : 0
  const avgMinutes = Math.floor(avgDurationSeconds / 60)
  const avgSeconds = avgDurationSeconds % 60
  const avgCallDuration = `${avgMinutes}m ${avgSeconds}s`

  const avgCallCost = totalCalls > 0 ? (totalCost / totalCalls).toFixed(2) : '0.00'
  const successRate = totalCalls > 0 ? Math.round((successfulCalls / totalCalls) * 100) : 0

  return {
    totalCalls,
    avgCallDuration,
    totalBalance: `₹${currentBalance.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
    avgCallCost: `₹${avgCallCost}`,
    successRate: `${successRate}%`,
    totalReservations: successfulCalls,
    lastRefreshed: new Date().toLocaleTimeString(),
  }
}

// Function to get chart data for calls per day
export function getCallsPerDayData(data: CallData[]) {
  const last14Days = Array.from({ length: 14 }, (_, i) => {
    const date = new Date()
    date.setDate(date.getDate() - (13 - i))
    return date
  })

  return last14Days.map(date => {
    const dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    const callsOnDate = data.filter(call => {
      const callDate = new Date(call.call_start)
      return callDate.toDateString() === date.toDateString()
    }).length

    return {
      date: dateStr,
      calls: callsOnDate
    }
  })
}

// Function to get call duration distribution data
export function getCallDurationData(data: CallData[]) {
  const shortCalls = data.filter(call => {
    const [minutes] = call.duration.split('m ')
    return parseInt(minutes) < 1
  }).length

  const mediumCalls = data.filter(call => {
    const [minutes] = call.duration.split('m ')
    const mins = parseInt(minutes)
    return mins >= 1 && mins <= 3
  }).length

  const longCalls = data.filter(call => {
    const [minutes] = call.duration.split('m ')
    return parseInt(minutes) > 3
  }).length

  return [
    { name: "< 1 min", value: shortCalls, color: "#06B6D4" },
    { name: "1-3 min", value: mediumCalls, color: "#8B5CF6" },
    { name: "> 3 min", value: longCalls, color: "#F59E0B" },
  ]
}
