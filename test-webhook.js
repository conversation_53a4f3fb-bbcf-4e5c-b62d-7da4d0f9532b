// Simple test script to verify webhook integration
// Run with: node test-webhook.js

const WEBHOOK_URL = "https://hook.eu2.make.com/xr96mpnk747rzostpd736mxcm87axq7l"
const TEST_URL = "http://localhost:3000/api/test-webhook"

async function testWebhook(url, name) {
  console.log(`\n🔍 Testing ${name}...`)
  console.log(`URL: ${url}`)
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    
    console.log(`✅ Success! Received ${Array.isArray(data) ? data.length : 0} records`)
    
    if (Array.isArray(data) && data.length > 0) {
      console.log(`📋 Sample record:`)
      console.log(`   ID: ${data[0].id || 'N/A'}`)
      console.log(`   Caller: ${data[0].caller_name || data[0].name || 'N/A'}`)
      console.log(`   Phone: ${data[0].phone || data[0].phone_number || 'N/A'}`)
      console.log(`   Duration: ${data[0].duration || 'N/A'}`)
      console.log(`   Success: ${data[0].success_flag || data[0].success || 'N/A'}`)
      console.log(`   Cost: ₹${data[0].cost || data[0].call_cost || 'N/A'}`)
    }
    
    return data
  } catch (error) {
    console.log(`❌ Failed: ${error.message}`)
    return null
  }
}

async function calculateTestMetrics(data) {
  if (!data || !Array.isArray(data) || data.length === 0) {
    console.log(`\n📊 No data available for metrics calculation`)
    return
  }

  const totalCalls = data.length
  const successfulCalls = data.filter(call => 
    call.success_flag !== undefined ? call.success_flag : call.success
  ).length
  const totalCost = data.reduce((sum, call) => 
    sum + parseFloat(call.cost || call.call_cost || 0), 0
  )
  const initialBalance = 5000
  const currentBalance = initialBalance - totalCost
  const successRate = Math.round((successfulCalls / totalCalls) * 100)
  const avgCost = totalCost / totalCalls

  console.log(`\n📊 Calculated Metrics:`)
  console.log(`   Total Calls: ${totalCalls}`)
  console.log(`   Successful Calls: ${successfulCalls}`)
  console.log(`   Success Rate: ${successRate}%`)
  console.log(`   Total Cost: ₹${totalCost.toFixed(2)}`)
  console.log(`   Average Cost: ₹${avgCost.toFixed(2)}`)
  console.log(`   Initial Balance: ₹${initialBalance.toFixed(2)}`)
  console.log(`   Current Balance: ₹${currentBalance.toFixed(2)}`)
}

async function main() {
  console.log(`🚀 Voice Assistant Dashboard - Webhook Integration Test`)
  console.log(`=`.repeat(60))

  // Test main webhook
  const mainData = await testWebhook(WEBHOOK_URL, "Main Webhook (Make.com)")
  
  // Test local endpoint
  const testData = await testWebhook(TEST_URL, "Test Endpoint (Local)")
  
  // Calculate metrics for available data
  const dataToAnalyze = mainData || testData
  await calculateTestMetrics(dataToAnalyze)
  
  console.log(`\n✨ Test completed!`)
  console.log(`💡 If main webhook failed, the dashboard will automatically use test data.`)
  console.log(`🌐 Visit http://localhost:3000 to see the dashboard in action.`)
}

// Run the test
main().catch(console.error)
